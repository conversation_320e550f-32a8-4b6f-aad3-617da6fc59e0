<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>Discourse</title>
    <link href="dis_crossculture.css" rel="stylesheet">

     

    <style>
        p.no-wrap-red {
            white-space: nowrap;
            display: inline;
            color: red;
        }

        p.no-wrap {
            white-space: nowrap;
            display: inline;
        }

        .key-point_1 {
            font-size: 20px;
            /* 更大的字体大小 */
            font-weight: bold;
            color: #3235f3;
            /* 突出显示的颜色 */
        }

        /* 新增样式：波形图 */
        #waveform {
            width: 100%;
            height: 100px;
            background-color: #f3f3f3;
            border: 1px solid #ccc;
            margin-top: 20px;
            display: none;
            /* 初始隐藏 */
        }

        .progress-container {
            width: 100%;
            background-color: #ddd;
            margin-bottom: 20px;
        }

        .progress-bar {
            height: 30px;
            background-color: #4caf50;
            text-align: center;
            line-height: 30px;
            color: white;
        }

        .container {
            padding: 20px;
        }

        .btn {
            margin: 5px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
        }

        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .audio-player {
            width: 100%;
            margin-top: 10px;
        }

        .status-text {
            margin-top: 10px;
            font-size: 18px;
        }
    </style>

</head>

<body>
    <div class="progress-container">
        <div class="progress-bar" id="progress_bar" style="width:70%;">70%</div>
    </div>
    <div class="container">
        <p class="question" style="margin-bottom: 5%">
            Please say as much as you can in <strong><span class="no-wrap-red" style='color: #d9534f'
                    id="word_time"></span></strong> minutes
        </p>
        <p class="key-point_1" id="vftIns"></p>
        <p id="sample" style="display: none"></p>
        Please click on "Start" when you are ready <br>
        <button class="btn" id="startRecord">Start</button>
        <button class="btn" disabled id="stopRecord">Stop</button>
        <audio class="audio-player" controls id="audio"></audio>
        <p class="status-text" id="status">Awaiting recording</p>
        <button class="btn" id="nextQuestion">Next task</button>
        <button class="btn" id="skipQuestion" style="display: none">Skip</button>
        <!-- <button class="btn" disabled id="reUpload" style="display: none">Upload retry</button> -->
        <button class="btn" disabled id="reUpload">Upload(retry)</button>
        <!-- <button class="btn" disabled id="reskip" style="display: none">Switch retry</button> -->
        <button class="btn" disabled id="reskip">Next task(retry)</button>
        <div id="savingStatus" style="display: none;">Saving...</div>

        <!-- 新增：波形图 -->
        <canvas id="waveform"></canvas>
    </div>

    <script>
        // 获取URL参数中的idNumber
        // var idNumber = 'yjhtest';
        var idNumber = decodeURIComponent(location.search.match(/[^\?&]*idNumber=([^&]*)/)[1]);
        let startRecord = document.getElementById('startRecord');
        let stopRecord = document.getElementById('stopRecord');
        let audio = document.getElementById('audio');
        let statusText = document.getElementById('status');
        let nextQuestion = document.getElementById('nextQuestion');
        let skipQuestion = document.getElementById('skipQuestion');
        let now_vft_ins = document.getElementById('vftIns');
        let now_vft_time = document.getElementById('word_time');
        let now_vft_sample = document.getElementById('sample');
        let reUpload = document.getElementById('reUpload');
        let reskip = document.getElementById('reskip');
        let waveform = document.getElementById('waveform');
        let mediaRecorder;
        let audioChunks = [];
        let recordingDuration = 0;
        let recordingInterval;
        let skip_onclicked = false;
        let stopButtonTimeout; // 新增：用于存储停止按钮的延时器ID
        var vft_id_initial = [0, 1, 2, 3, 4, 5]; // 0:动物的名称 1:第一个字的拼音是b开头的词语 2:情绪相关的词语
        var vft_time = [5, 5, 5, 5, 5, 5]; // 5分钟 时间 talk
        var vft_sample = ["", "", "e.g. book, bar", "e.g. 波浪（bo lang）、伯父（bo fu）。", "", ""]; // 任务样例 [Q]
        var vft_ins = [
            "Please say words in <span style='color: #d9534f; font-size:30px'><strong>English</strong></span> that express people's <span style='color: #d9534f; font-size:30px'><strong>emotions</strong></span>.",
            "Please say words in <span style='color: #8fbc8f; font-size:30px'><strong>Chinese</strong></span> that express people's <span style='color: #d9534f; font-size:30px'><strong>emotions</strong></span>.",
            "Please say words in <span style='color: #d9534f; font-size:30px'><strong>English</strong></span> that start with the <span style='color: #d9534f; font-size:30px'><strong>letter 'b'</strong></span>.",
            "Please say words in <span style='color: #8fbc8f; font-size:30px'><strong>Chinese</strong></span> that start with the <span style='color: #d9534f; font-size:30px'><strong>pinyin 'b'</strong></span>.",
            "Please say words in <span style='color: #d9534f; font-size:30px'><strong>English</strong></span> that are related to <span style='color: #d9534f; font-size:30px'><strong>food</strong></span>.",
            "Please say words in <span style='color: #8fbc8f; font-size:30px'><strong>Chinese</strong></span> that are related to <span style='color: #d9534f; font-size:30px'><strong>food</strong></span>."
        ]; // 指导语

        // 读取存储的currentIndex，可以刷新
        var currentIndex = JSON.parse(sessionStorage.getItem('vft_currentIndex_storage'));
        if (currentIndex !== null && currentIndex !== undefined) {
            console.log('Read index successfully');
        } else {
            var currentIndex = 0;
            console.log('Initial currentIndex');
        }
        let progress_bar = document.getElementById('progress_bar');

        // 保存进度并打乱vft_id
        function shuffleArray(array) {
            let newArr = [...array]; // 创建原数组的一个副本，避免改变原数组
            for (let i = newArr.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [newArr[i], newArr[j]] = [newArr[j], newArr[i]]; // 在副本上进行元素交换
            }
            return newArr; // 返回新的打乱后的数组
        }

        var vft_id = JSON.parse(sessionStorage.getItem('vft_id_storage'));
        if (vft_id !== null && vft_id !== undefined) {
            console.log('Read vft_id successfully');
        } else {
            var vft_id = shuffleArray(vft_id_initial);
            sessionStorage.setItem('vft_id_storage', JSON.stringify(vft_id));
            console.log('Initial vft_id');
        }

        showWord();

        function showWord() {
            statusText.textContent = "Awaiting recording";
            skip_onclicked = false;
            reUpload.disabled = true;
            reskip.disabled = true;
            nextQuestion.disabled = true;
            skipQuestion.disabled = true;
            startRecord.disabled = false;
            stopRecord.disabled = true; // 确保停止按钮禁用
            waveform.style.display = 'none'; // 隐藏波形图

            // 如果有之前的停止按钮计时器，清除它
            if (stopButtonTimeout) {
                clearTimeout(stopButtonTimeout);
            }

            if (currentIndex >= vft_ins.length) {
                window.location.href = 'cross_culture_end_vft_en.html?idNumber=' + encodeURIComponent(idNumber); // 跳转地址 [change]
                return;
            }

            let width_progress = 100 * (currentIndex + 1) / vft_ins.length;
            progress_bar.style.width = width_progress + '%';
            progress_bar.innerHTML = `${currentIndex + 1}/${vft_ins.length}`;

            now_vft_ins.innerHTML = vft_ins[vft_id[currentIndex]]
            now_vft_time.textContent = vft_time[vft_id[currentIndex]]
            now_vft_sample.textContent = vft_sample[vft_id[currentIndex]]
            sessionStorage.setItem('vft_currentIndex_storage', currentIndex); // 保存currentIndex
            currentIndex++;
            nextQuestion.disabled = true;
            startRecord.textContent = "Start"; // 更改按钮文本
        }

        document.addEventListener('DOMContentLoaded', function () {
            // 设置 skipQuestion 按钮初始状态为不可点击
            skipQuestion.disabled = true;
            startRecord.onclick = function () {
                // 禁用停止按钮，直到指定时间后才启用
                stopRecord.disabled = true;

                // 显示波形图
                waveform.style.display = 'block';

                // 重置波形图
                const canvas = document.getElementById('waveform');
                const canvasCtx = canvas.getContext('2d');
                canvasCtx.clearRect(0, 0, canvas.width, canvas.height);

                skipQuestion.disabled = false;
                // 隐藏录音进度条
                audio.style.display = 'none';
                audioChunks = [];
                audio.src = '';

                // 显示获取麦克风权限的状态
                statusText.textContent = "Requesting microphone permission...";
                
                navigator.mediaDevices.getUserMedia({ audio: true })
                    .then(stream => {
                        let options = {};
                        // Force WAV format for recording
                        // Note: MediaRecorder doesn't directly support WAV format
                        // We'll convert to WAV after recording
                        if (MediaRecorder.isTypeSupported('audio/webm')) {
                            options.mimeType = 'audio/webm';
                        } else if (MediaRecorder.isTypeSupported('audio/ogg')) {
                            options.mimeType = 'audio/ogg';
                        } else {
                            console.log('Using browser default audio format.');
                        }
                        mediaRecorder = new MediaRecorder(stream, options);

                        // 初始化音频上下文和分析器
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const source = audioContext.createMediaStreamSource(stream);
                        const analyser = audioContext.createAnalyser();
                        source.connect(analyser);
                        analyser.fftSize = 2048;
                        const bufferLength = analyser.fftSize;
                        const dataArray = new Uint8Array(bufferLength);

                        // 获取 Canvas 上下文
                        const canvas = document.getElementById('waveform');
                        const canvasCtx = canvas.getContext('2d');

                        // 设置 Canvas 尺寸
                        function resizeCanvas() {
                            canvas.width = canvas.clientWidth;
                            canvas.height = canvas.clientHeight;
                        }

                        resizeCanvas();
                        window.addEventListener('resize', resizeCanvas);

                        function drawWaveform() {
                            requestAnimationFrame(drawWaveform);

                            analyser.getByteTimeDomainData(dataArray);

                            canvasCtx.fillStyle = '#f3f3f3';
                            canvasCtx.fillRect(0, 0, canvas.width, canvas.height);

                            canvasCtx.lineWidth = 2;
                            canvasCtx.strokeStyle = '#d9534f';

                            canvasCtx.beginPath();

                            const sliceWidth = canvas.width * 1.0 / bufferLength;
                            let x = 0;

                            for (let i = 0; i < bufferLength; i++) {
                                const v = dataArray[i] / 128.0;
                                const y = v * canvas.height / 2;

                                if (i === 0) {
                                    canvasCtx.moveTo(x, y);
                                } else {
                                    canvasCtx.lineTo(x, y);
                                }

                                x += sliceWidth;
                            }

                            canvasCtx.lineTo(canvas.width, canvas.height / 2);
                            canvasCtx.stroke();
                        }

                        drawWaveform(); // 开始绘制波形

                        mediaRecorder.ondataavailable = function (e) {
                            audioChunks.push(e.data);
                        };
                        mediaRecorder.onstop = function () {
                            let audioBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
                            console.log("Recorded Blob size:", audioBlob.size); // 调试：检查Blob大小
                            console.log("Used MIME type:", mediaRecorder.mimeType); // 调试：检查使用的MIME类型

                            let audioUrl = URL.createObjectURL(audioBlob);
                            audio.src = audioUrl;
                            clearInterval(recordingInterval);

                            // 清除停止按钮的计时器
                            if (stopButtonTimeout) {
                                clearTimeout(stopButtonTimeout);
                            }

                            // 隐藏波形图
                            waveform.style.display = 'none';

                            if (skip_onclicked == true) {
                                skipQuestion.disabled = true;
                                startRecord.disabled = true;
                                stopRecord.disabled = true;
                                statusText.textContent = "Recording stopped";
                                nextQuestion.disabled = true;
                                audio.style.display = 'block';
                                reUpload.disabled = true;
                                uploadAudio(audioBlob).then((result) => {
                                    showWord();
                                });
                            }
                        };
                        
                        // 开始录音
                        mediaRecorder.start();
                        
                        // 现在权限已获取且录音已开始，开始倒计时以启用停止按钮
                        // 在这里设置计时器，而不是在点击开始按钮时立即设置
                        const recordingTimeInMs = 1000 * vft_time[vft_id[currentIndex - 1]] * 60;
                        stopButtonTimeout = setTimeout(function () {
                            stopRecord.disabled = false;
                        }, recordingTimeInMs);
                        
                        // 重置并开始录音计时
                        recordingDuration = 0;
                        recordingInterval = setInterval(function () {
                            recordingDuration++;
                            statusText.textContent = "On recording ｜ Time：" + recordingDuration + " seconds";
                        }, 1000);

                        stopRecord.disabled = true; // 确保停止按钮禁用
                        startRecord.disabled = true;
                        startRecord.textContent = "Recording";
                        nextQuestion.disabled = true;
                        statusText.textContent = "Recording started";
                    })
                    .catch(error => {
                        console.error('Error accessing microphone:', error);
                        alert('无法访问麦克风，请检查权限设置。');
                        waveform.style.display = 'none'; // 确保在错误时隐藏波形图
                        statusText.textContent = "Error: Could not access microphone";
                        startRecord.disabled = false; // 重新启用开始按钮，让用户可以重试
                    });
            };

            skipQuestion.onclick = function () {
                if (recordingDuration < 120) {
                    alert("The recording time will take more than 2 minutes, come on, please say a little more.");
                    return;
                } else {
                    skip_onclicked = true;
                    mediaRecorder.stop();
                }
            }

            stopRecord.onclick = function () {
                if (recordingDuration < vft_time[vft_id[currentIndex - 1]] * 60) {
                    alert("The recording needs to reach the specified duration, please continue speaking.");
                    return;
                }
                mediaRecorder.stop();
                skipQuestion.disabled = true;
                startRecord.disabled = true;
                stopRecord.disabled = true;
                startRecord.textContent = 'Recorded'
                statusText.textContent = "Recording has stopped";
                nextQuestion.disabled = false;
                audio.style.display = 'block';
            };

            nextQuestion.onclick = function () {
                nextQuestion.disabled = true;
                skipQuestion.disabled = true;
                statusText.textContent = 'Saving';
                let audioBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
                uploadAudio(audioBlob).then((result) => {
                    console.log('Promise Resolved, the data is:', result);
                    showWord();
                });
            };

            reskip.onclick = function () {
                showWord();
            };

            reUpload.onclick = function () {
                let audioBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
                uploadAudio(audioBlob).then((result) => {
                    reskip.disabled = false;
                });
            };

            function uploadAudio(blob) {
                console.log('Start uploading audio to server');
                console.log("Original Blob size:", blob.size);
                console.log("Original Blob type:", blob.type);
                var timestamp = Math.floor(Date.now() / 1000);

                // Show saving status
                document.getElementById('savingStatus').style.display = 'block';
                statusText.textContent = "Preparing audio for upload...";

                // Convert blob to WAV format
                return blobToWav(blob).then(wavBlob => {
                    console.log("Converted WAV Blob size:", wavBlob.size);
                    console.log("Converted WAV Blob type:", wavBlob.type);
                    
                    const audioFileName = idNumber + '_' + "vft" + (vft_id[currentIndex - 1] + 1) + "_" + timestamp + '.wav';
                    console.log("Generated filename:", audioFileName);
                    
                    // Create FormData for upload
                    const formData = new FormData();
                    formData.append('file', wavBlob, audioFileName);
                    formData.append('idNumber', idNumber);
                    formData.append('taskId', vft_id[currentIndex - 1] + 1);
                    formData.append('timestamp', timestamp);
                    
                    console.log("FormData prepared, sending to server...");
                    statusText.textContent = "Uploading to server...";
                    
                    // Upload to server
                    return fetch('/upload', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Server returned ' + response.status);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Upload successful:', data);
                        document.getElementById('savingStatus').style.display = 'none';
                        
                        // Show success message to user
                        statusText.textContent = "Upload successful!";
                        
                        return data.filename;
                    })
                    .catch(error => {
                        console.error('Upload failed:', error);
                        document.getElementById('savingStatus').style.display = 'none';
                        
                        // Show error message to user
                        statusText.textContent = "Upload failed. Saving locally instead.";
                        
                        // Fallback to localStorage if server upload fails
                        console.log('Falling back to localStorage');
                        const reader = new FileReader();
                        return new Promise((resolve, reject) => {
                            reader.onload = function() {
                                const audioData = {
                                    name: audioFileName,
                                    data: reader.result, // base64 data
                                    timestamp: timestamp,
                                    taskId: vft_id[currentIndex - 1] + 1,
                                    idNumber: idNumber
                                };
                                
                                // Save to localStorage
                                localStorage.setItem(audioFileName, JSON.stringify(audioData));
                                console.log('Audio saved to localStorage with key:', audioFileName);
                                
                                // Update status to show local save was successful
                                statusText.textContent = "Saved locally. You can try uploading again later.";
                                
                                resolve(audioFileName);
                            };
                            reader.onerror = function() {
                                console.error('Error reading blob');
                                statusText.textContent = "Error: Could not save audio locally.";
                                reject(new Error('Failed to read audio data'));
                            };
                            reader.readAsDataURL(wavBlob);
                        });
                    });
                });
            }

            // Function to convert audio blob to WAV format
            function blobToWav(blob) {
                return new Promise((resolve, reject) => {
                    // If the blob is already in WAV format, return it directly
                    if (blob.type === 'audio/wav' || blob.type === 'audio/wave') {
                        resolve(blob);
                        return;
                    }
                    
                    // Create an audio context to decode the audio data
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    
                    // Read the blob as an array buffer
                    const reader = new FileReader();
                    reader.onload = function() {
                        // Decode the audio data
                        audioContext.decodeAudioData(reader.result, function(buffer) {
                            // Convert the decoded audio to WAV format
                            const wavBlob = audioBufferToWav(buffer);
                            resolve(wavBlob);
                        }, function(error) {
                            console.error('Error decoding audio data:', error);
                            // If decoding fails, just return the original blob with .wav extension
                            resolve(new Blob([blob], { type: 'audio/wav' }));
                        });
                    };
                    
                    reader.onerror = function() {
                        console.error('Error reading blob');
                        reject(new Error('Failed to read audio data'));
                    };
                    
                    reader.readAsArrayBuffer(blob);
                });
            }
            
            // Function to convert AudioBuffer to WAV format
            function audioBufferToWav(buffer) {
                const numChannels = buffer.numberOfChannels;
                const sampleRate = buffer.sampleRate;
                const format = 1; // PCM
                const bitDepth = 16;
                
                // Create a buffer for the WAV file
                const result = new ArrayBuffer(44 + buffer.length * numChannels * 2);
                const view = new DataView(result);
                
                // Write the WAV header
                writeString(view, 0, 'RIFF');
                view.setUint32(4, 36 + buffer.length * numChannels * 2, true);
                writeString(view, 8, 'WAVE');
                writeString(view, 12, 'fmt ');
                view.setUint32(16, 16, true); // Subchunk1Size (16 for PCM)
                view.setUint16(20, format, true);
                view.setUint16(22, numChannels, true);
                view.setUint32(24, sampleRate, true);
                view.setUint32(28, sampleRate * numChannels * 2, true); // Byte rate
                view.setUint16(32, numChannels * 2, true); // Block align
                view.setUint16(34, bitDepth, true);
                writeString(view, 36, 'data');
                view.setUint32(40, buffer.length * numChannels * 2, true);
                
                // Write the PCM data
                let offset = 44;
                for (let i = 0; i < buffer.length; i++) {
                    for (let channel = 0; channel < numChannels; channel++) {
                        const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
                        view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
                        offset += 2;
                    }
                }
                
                return new Blob([result], { type: 'audio/wav' });
            }
            
            function writeString(view, offset, string) {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            }

            // Local storage implementation - no upload needed
            function test(objectName, uploadFile) {
                console.log('Using local storage, no upload needed');
                return Promise.resolve('local://' + objectName);
            }

            function getFileExtension(mimeType) {
                // Always return .wav extension as we convert all recordings to WAV
                return '.wav';
            }
        });
    </script>
</body>
</html>